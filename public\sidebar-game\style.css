/* Additional styles for Sidebar Game */

/* Custom scrollbar for game content */
#game-content::-webkit-scrollbar {
    width: 6px;
}

#game-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#game-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#game-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Code syntax highlighting */
.code-highlight {
    background: #1a1a1a;
    color: #f8f8f2;
    padding: 1rem;
    border-radius: 0.5rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    overflow-x: auto;
}

.code-highlight .keyword {
    color: #66d9ef;
}

.code-highlight .string {
    color: #a6e22e;
}

.code-highlight .comment {
    color: #75715e;
    font-style: italic;
}

.code-highlight .number {
    color: #ae81ff;
}

/* Game type button hover effects */
.game-type-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Flexbox preview styles */
.flexbox-preview {
    border: 2px dashed #e5e7eb;
    background: #f9fafb;
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
}

.flexbox-preview .pond {
    border: 2px solid #10b981;
    background: linear-gradient(135deg, #34d399, #10b981);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
    min-width: 150px;
    min-height: 80px;
}

.flexbox-preview .frog {
    font-size: 1.5rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Multiple choice option styles */
.mcq-option {
    transition: all 0.2s ease;
    cursor: pointer;
}

.mcq-option:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

.mcq-option.selected {
    background-color: #dbeafe;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Feedback animations */
.feedback-enter {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Success pulse effect */
.success-pulse {
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
    }
}

/* Error shake effect */
.error-shake {
    animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Leaderboard styles */
.leaderboard-entry {
    transition: all 0.2s ease;
}

.leaderboard-entry:hover {
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.leaderboard-rank {
    font-weight: bold;
    font-size: 1.1rem;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
    #game-sidebar {
        width: 100vw;
        left: 0;
        right: 0;
    }
    
    .game-type-btn {
        font-size: 0.75rem;
        padding: 0.5rem;
    }
    
    #game-input {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    #game-sidebar {
        background-color: #1f2937;
        border-color: #374151;
        color: #f9fafb;
    }
    
    .code-highlight {
        background: #111827;
        border: 1px solid #374151;
    }
    
    .flexbox-preview {
        background: #374151;
        border-color: #4b5563;
    }
}

/* Print styles */
@media print {
    #game-sidebar,
    #leaderboard-modal {
        display: none !important;
    }
}
