@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* PWA Specific Styles */
@layer utilities {
  .pwa-installed {
    /* Styles for when app is installed */
  }

  /* PWA Animation Classes */
  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* PWA Install Button Pulse Animation */
@keyframes pulse-install {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
}

.animate-pulse-install {
  animation: pulse-install 2s infinite;
}

@layer base {
  :root {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    font-weight: 400;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';

    color-scheme: light dark;
    color: #0f172a;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);

    &.dark {
      color: #f1f5f9;
      background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    }

    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
  }

  * {
    box-sizing: border-box;
  }

  *::before,
  *::after {
    box-sizing: border-box;
  }

  body {
    margin: 0;
    min-width: 320px;
    min-height: 100vh;
    overflow-x: hidden;
    background: inherit;
  }

  /* Enhanced focus styles */
  *:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }

  *:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
    border-radius: 4px;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Selection styles */
  ::selection {
    background-color: #3b82f6;
    color: white;
  }

  .dark ::selection {
    background-color: #60a5fa;
    color: #0f172a;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background-color: #f3f4f6;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 9999px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #9ca3af;
  }

  .dark ::-webkit-scrollbar-track {
    background-color: #1f2937;
  }

  .dark ::-webkit-scrollbar-thumb {
    background-color: #4b5563;
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background-color: #6b7280;
  }
}

@layer components {
  /* Enhanced Button Styles */
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.75rem;
    transition: all 0.2s ease-out;
    outline: none;
    position: relative;
    overflow: hidden;
  }

  .btn:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .btn:hover::before {
    left: 100%;
  }

  .btn-primary {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    color: white;
    box-shadow: 0 4px 25px -5px rgba(59, 130, 246, 0.15);
    transform: scale(1);
    transition: all 0.2s ease-out;
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    box-shadow: 0 10px 40px -10px rgba(59, 130, 246, 0.3);
    transform: scale(1.05);
  }

  .btn-secondary {
    background-color: white;
    color: #374151;
    border: 1px solid #d1d5db;
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
    transform: scale(1);
    transition: all 0.2s ease-out;
  }

  .btn-secondary:hover {
    background-color: #f9fafb;
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
  }

  .dark .btn-secondary {
    background-color: #1f2937;
    color: #e5e7eb;
    border-color: #4b5563;
  }

  .dark .btn-secondary:hover {
    background-color: #374151;
  }

  /* Enhanced Card Styles */
  .card {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(229, 231, 235, 0.5);
    border-radius: 1rem;
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
    transition: all 0.3s ease-out;
  }

  .dark .card {
    background-color: rgba(31, 41, 55, 0.8);
    border-color: rgba(75, 85, 99, 0.5);
  }

  .card-hover {
    transform: scale(1);
    transition: all 0.3s ease-out;
  }

  .card-hover:hover {
    transform: scale(1.05) translateY(-4px);
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
  }

  .card-gradient {
    background: linear-gradient(135deg, #ffffff, #f9fafb);
    border: none;
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.1);
  }

  .dark .card-gradient {
    background: linear-gradient(135deg, #1f2937, #111827);
  }

  /* Glass Effect */
  .glass {
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass {
    background-color: rgba(17, 24, 39, 0.1);
    border-color: rgba(75, 85, 99, 0.2);
  }

  /* Gradient Text */
  .text-gradient {
    background: linear-gradient(135deg, #2563eb, #d946ef);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }

  .text-gradient-warm {
    background: linear-gradient(135deg, #f59e0b, #ef4444);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }

  .text-gradient-cool {
    background: linear-gradient(135deg, #3b82f6, #22c55e);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }

  /* Progress Bar Enhancements */
  .progress-bar {
    width: 100%;
    background-color: #e5e7eb;
    border-radius: 9999px;
    overflow: hidden;
  }

  .dark .progress-bar {
    background-color: #374151;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    border-radius: 9999px;
    transition: all 0.5s ease-out;
    box-shadow: 0 4px 25px -5px rgba(59, 130, 246, 0.15);
  }

  /* Badge Styles */
  .badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .badge-primary {
    background-color: #dbeafe;
    color: #1e40af;
  }

  .dark .badge-primary {
    background-color: rgba(37, 99, 235, 0.3);
    color: #93c5fd;
  }

  .badge-success {
    background-color: #dcfce7;
    color: #166534;
  }

  .dark .badge-success {
    background-color: rgba(34, 197, 94, 0.3);
    color: #86efac;
  }

  .badge-warning {
    background-color: #fef3c7;
    color: #92400e;
  }

  .dark .badge-warning {
    background-color: rgba(245, 158, 11, 0.3);
    color: #fcd34d;
  }

  .badge-error {
    background-color: #fee2e2;
    color: #991b1b;
  }

  .dark .badge-error {
    background-color: rgba(239, 68, 68, 0.3);
    color: #fca5a5;
  }

  /* Hover Effects */
  .hover-lift {
    transition: transform 0.2s ease-out;
  }

  .hover-lift:hover {
    transform: scale(1.05) translateY(-4px);
  }

  .hover-glow {
    transition: box-shadow 0.3s ease-out;
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  /* Loading Spinner */
  .spinner {
    animation: spin 1s linear infinite;
    border-radius: 50%;
    border: 2px solid #d1d5db;
    border-top-color: #2563eb;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes spin-slow {
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes bounce-soft {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
  }

  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-bounce-soft {
    animation: bounce-soft 2s ease-in-out infinite;
  }

  .animate-spin-slow {
    animation: spin-slow 8s linear infinite;
  }

  /* PWA Install Prompt Animations */
  @keyframes slideInUp {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    }
    50% {
      box-shadow: 0 0 30px rgba(59, 130, 246, 0.6), 0 0 40px rgba(147, 51, 234, 0.3);
    }
  }

  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  .animate-slide-in-up {
    animation: slideInUp 0.5s ease-out;
  }

  .animate-glow {
    animation: glow 3s ease-in-out infinite;
  }

  .animate-gradient-shift {
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
  }

  /* Line clamp utility */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Gradient text utility */
  .text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Enhanced hover effects */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  }

  /* PWA and Mobile Enhancements */
  .pwa-safe-area {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Video Container Responsive */
  .video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    overflow: hidden;
    border-radius: 1rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  }

  .video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 1rem;
  }

  /* Enhanced Mobile Responsiveness */
  @media (max-width: 640px) {
    .card {
      margin: 0.5rem;
      padding: 1rem;
    }

    .btn {
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      touch-action: manipulation;
    }

    .video-container {
      margin: 1rem 0;
      border-radius: 0.5rem;
    }

    /* Mobile-specific animations */
    .hover-lift:hover {
      transform: translateY(-2px);
    }

    /* Touch-friendly interactions */
    .touch-manipulation {
      touch-action: manipulation;
    }

    /* Safe area support for devices with notches */
    .safe-area-pb {
      padding-bottom: env(safe-area-inset-bottom);
    }

    .pb-safe-area-inset-bottom {
      padding-bottom: calc(1rem + env(safe-area-inset-bottom));
    }

    /* Mobile-specific spacing */
    .mobile-spacing {
      margin-bottom: 5rem; /* Space for bottom navigation */
    }

    /* Improved touch targets */
    .touch-target {
      min-height: 44px;
      min-width: 44px;
    }

    /* Interactive lesson cards */
    .lesson-card {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .lesson-card:active {
      transform: scale(0.98);
    }

    /* Line clamp utilities for mobile */
    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .line-clamp-3 {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    /* Reduce motion for mobile */
    @media (prefers-reduced-motion: reduce) {
      .animate-bounce-soft,
      .animate-float,
      .animate-pulse,
      .animate-spin-slow {
        animation: none;
      }

      .hover-lift {
        transition: none;
      }
    }
  }

  /* Tablet Responsiveness */
  @media (min-width: 641px) and (max-width: 1024px) {
    .card {
      padding: 1.5rem;
    }

    .video-container {
      margin: 1.5rem 0;
    }

    /* Tablet-specific bottom navigation adjustments */
    .bottom-nav-tablet {
      padding: 0.75rem 1rem;
    }
  }

  /* Desktop Enhancements */
  @media (min-width: 1025px) {
    .card:hover {
      transform: scale(1.02) translateY(-4px);
    }

    .video-container {
      margin: 2rem 0;
    }
  }

  /* High DPI Displays */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .card {
      border-width: 0.5px;
    }
  }

  /* Dark Mode Enhancements */
  @media (prefers-color-scheme: dark) {
    :root {
      color-scheme: dark;
    }

    .video-container {
      box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    }
  }

  /* Print Styles */
  @media print {
    .video-container,
    .btn,
    .animate-bounce-soft,
    .animate-float,
    .animate-pulse {
      display: none !important;
    }

    .card {
      break-inside: avoid;
      box-shadow: none;
      border: 1px solid #ccc;
    }
  }

  /* Focus Improvements for Accessibility */
  .focus-ring:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
    border-radius: 0.375rem;
  }

  /* Loading States */
  .loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }

  @keyframes loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }

  /* Utility Classes for PWA */
  .fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
  }

  .offline-indicator {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #ef4444;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    z-index: 1000;
    animation: slideUp 0.3s ease-out;
  }

  @keyframes slideUp {
    from {
      transform: translateX(-50%) translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateX(-50%) translateY(0);
      opacity: 1;
    }
  }
}
