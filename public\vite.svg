<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
  <defs>
    <linearGradient id="learnhub-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3b82f6"/>
      <stop offset="50%" stop-color="#8b5cf6"/>
      <stop offset="100%" stop-color="#ec4899"/>
    </linearGradient>
  </defs>

  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#learnhub-gradient)" stroke="#ffffff" stroke-width="1"/>

  <!-- Book/Learning icon -->
  <g fill="#ffffff">
    <!-- Book base -->
    <rect x="8" y="10" width="16" height="12" rx="1" fill="#ffffff" opacity="0.9"/>

    <!-- Book pages -->
    <rect x="9" y="11" width="14" height="1" fill="url(#learnhub-gradient)" opacity="0.6"/>
    <rect x="9" y="13" width="12" fill="url(#learnhub-gradient)" opacity="0.6"/>
    <rect x="9" y="15" width="10" height="1" fill="url(#learnhub-gradient)" opacity="0.6"/>
    <rect x="9" y="17" width="8" height="1" fill="url(#learnhub-gradient)" opacity="0.6"/>

    <!-- Hub/connection dots -->
    <circle cx="12" cy="8" r="1.5" fill="#ffffff"/>
    <circle cx="20" cy="8" r="1.5" fill="#ffffff"/>
    <circle cx="24" cy="16" r="1.5" fill="#ffffff"/>
    <circle cx="20" cy="24" r="1.5" fill="#ffffff"/>
    <circle cx="12" cy="24" r="1.5" fill="#ffffff"/>
    <circle cx="8" cy="16" r="1.5" fill="#ffffff"/>

    <!-- Connection lines -->
    <line x1="12" y1="8" x2="16" y2="10" stroke="#ffffff" stroke-width="1" opacity="0.7"/>
    <line x1="20" y1="8" x2="16" y2="10" stroke="#ffffff" stroke-width="1" opacity="0.7"/>
    <line x1="24" y1="16" x2="22" y2="16" stroke="#ffffff" stroke-width="1" opacity="0.7"/>
    <line x1="20" y1="24" x2="16" y2="22" stroke="#ffffff" stroke-width="1" opacity="0.7"/>
    <line x1="12" y1="24" x2="16" y2="22" stroke="#ffffff" stroke-width="1" opacity="0.7"/>
    <line x1="8" y1="16" x2="10" y2="16" stroke="#ffffff" stroke-width="1" opacity="0.7"/>
  </g>
</svg>