<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LearnHub - Offline</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        .container {
            max-width: 500px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.8;
        }
        
        h1 {
            font-size: 2rem;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .features {
            margin-top: 30px;
            text-align: left;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .feature-icon {
            margin-right: 10px;
            font-size: 1.2rem;
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📚</div>
        <h1>You're Offline</h1>
        <p>Don't worry! You can still access your downloaded lessons and continue learning even without an internet connection.</p>
        
        <a href="/" class="retry-btn" onclick="window.location.reload()">
            Try Again
        </a>
        
        <div class="features">
            <div class="feature">
                <span class="feature-icon">✅</span>
                <span>Access downloaded lessons</span>
            </div>
            <div class="feature">
                <span class="feature-icon">📖</span>
                <span>Continue reading materials</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🎯</span>
                <span>Practice with offline quizzes</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🔄</span>
                <span>Sync progress when back online</span>
            </div>
        </div>
    </div>

    <script>
        // Check for connectivity and auto-reload when back online
        window.addEventListener('online', () => {
            window.location.reload();
        });
        
        // Show connection status
        if (navigator.onLine) {
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }
    </script>
</body>
</html>
