{"version": 2, "buildCommand": "npm run build", "outputDirectory": "dist", "installCommand": "npm install", "framework": "vite", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}, {"key": "Service-Worker-Allowed", "value": "/"}]}, {"source": "/workbox-(.+).js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}, {"source": "/manifest.webmanifest", "headers": [{"key": "Content-Type", "value": "application/manifest+json"}, {"key": "Cache-Control", "value": "public, max-age=86400"}]}, {"source": "/index.html", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}]}